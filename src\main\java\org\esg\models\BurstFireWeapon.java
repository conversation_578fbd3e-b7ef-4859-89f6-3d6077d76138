package org.esg.models;

import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import org.esg.Main;
import org.esg.enums.AmmoType;
import org.esg.enums.WeaponType;

import java.util.UUID;
import java.util.logging.Logger;

/**
 * Classe abstrata para armas com modo de disparo em rajadas.
 * Esta classe estende a classe base Weapon e implementa a lógica de disparo em rajadas.
 */
public abstract class BurstFireWeapon extends Weapon {

    private static final Logger LOGGER = Logger.getLogger(BurstFireWeapon.class.getName());

    /** Número de tiros em cada rajada */
    protected int burstCount = 3;

    /** Ticks de delay entre os tiros da mesma rajada */
    protected int burstDelay = 2;

    /** Flag que indica se a arma está em modo de rajada */
    protected boolean burstMode = true;

    /**
     * Construtor com suporte a rajadas.
     */
    protected BurstFireWeapon(String name, WeaponType type, AmmoType ammoType, double damage, double range,
                     double accuracy, double fireRate, double projectileSpeed, int maxAmmo,
                     int currentAmmo, int reloadTime, int projectileCount, double headshotMultiplier) {
        super(name, type, ammoType, damage, range, accuracy, fireRate, projectileSpeed, maxAmmo,
              currentAmmo, reloadTime, projectileCount, headshotMultiplier);
    }

    /**
     * Define o número de tiros em cada rajada.
     *
     * @param burstCount O número de tiros em cada rajada
     */
    public void setBurstCount(int burstCount) {
        this.burstCount = burstCount;
    }

    /**
     * Define o delay entre os tiros da mesma rajada.
     *
     * @param burstDelay O delay em ticks entre os tiros da mesma rajada
     */
    public void setBurstDelay(int burstDelay) {
        this.burstDelay = burstDelay;
    }

    /**
     * Define se a arma está em modo de rajada.
     *
     * @param burstMode true para habilitar o modo de rajada, false para desabilitar
     */
    public void setBurstMode(boolean burstMode) {
        this.burstMode = burstMode;
    }

    /**
     * Obtém o número de tiros em cada rajada.
     *
     * @return O número de tiros em cada rajada
     */
    public int getBurstCount() {
        return burstCount;
    }

    /**
     * Obtém o delay entre os tiros da mesma rajada.
     *
     * @return O delay em ticks entre os tiros da mesma rajada
     */
    public int getBurstDelay() {
        return burstDelay;
    }

    /**
     * Verifica se a arma está em modo de rajada.
     *
     * @return true se a arma estiver em modo de rajada, false caso contrário
     */
    public boolean isBurstMode() {
        return burstMode;
    }

    /**
     * Sobrescreve o método startFiring para implementar o disparo em rajadas.
     * Este método dispara um número fixo de tiros em sequência, com um pequeno delay entre eles.
     */
    @Override
    public void startFiring(Player player) {
        LOGGER.info("BurstFireWeapon.startFiring chamado para " + getName() +
                   " - BurstMode: " + burstMode +
                   ", BurstCount: " + burstCount +
                   ", BurstDelay: " + burstDelay);

        // Se não estiver em modo de rajada, usar o comportamento normal
        if (!burstMode) {
            LOGGER.info("BurstMode desabilitado, usando comportamento normal");
            super.startFiring(player);
            return;
        }

        // Se estiver tentando recarregar, cancelar disparo e iniciar recarga
        if (player.isSneaking() && canReload(player)) {
            // Cancelar qualquer estado de disparo e iniciar recarga
            UUID playerUUID = player.getUniqueId();
            getIsFiring().put(playerUUID, false);
            reload(player);
            return;
        }

        // Verificar se pode atirar
        if (canShoot(player)) {
            LOGGER.info("Iniciando rajada de " + burstCount + " tiros para " + getName());

            // Primeiro tiro imediato
            shoot(player);

            // Definir estado de disparo
            UUID playerUUID = player.getUniqueId();
            getIsFiring().put(playerUUID, true);

            // Agendar tiros restantes da rajada
            new BukkitRunnable() {
                private int shotsLeft = burstCount - 1; // -1 porque já disparamos o primeiro tiro

                @Override
                public void run() {
                    LOGGER.info("Rajada - Tiros restantes: " + shotsLeft +
                               ", Munição: " + getCurrentAmmo() +
                               ", IsFiring: " + getIsFiring().getOrDefault(playerUUID, false));

                    // Verificar se ainda tem munição e se ainda está atirando
                    if (shotsLeft <= 0 || !getIsFiring().getOrDefault(playerUUID, false) ||
                        getCurrentAmmo() <= 0 || isReloading(player)) {
                        LOGGER.info("Finalizando rajada - Motivo: " +
                                   (shotsLeft <= 0 ? "Tiros acabaram" :
                                    !getIsFiring().getOrDefault(playerUUID, false) ? "Não está mais atirando" :
                                    getCurrentAmmo() <= 0 ? "Sem munição" : "Recarregando"));
                        getIsFiring().put(playerUUID, false);
                        cancel();
                        return;
                    }

                    // Disparar
                    shoot(player);
                    shotsLeft--;

                    // Se acabaram os tiros da rajada, finalizar
                    if (shotsLeft <= 0) {
                        LOGGER.info("Rajada completa finalizada");
                        getIsFiring().put(playerUUID, false);
                        cancel();
                    }
                }
            }.runTaskTimer(Main.getPlugin(), burstDelay, burstDelay);
        } else {
            LOGGER.info("Não pode atirar - canShoot retornou false");
        }
    }
}