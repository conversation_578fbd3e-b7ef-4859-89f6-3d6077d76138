package org.esg.weapons;

import org.esg.enums.AmmoType;
import org.esg.models.BurstFireWeapon;
import org.esg.enums.WeaponType;
import java.util.logging.Logger;

public class M4A1 extends BurstFireWeapon {
    private static final Logger LOGGER = Logger.getLogger(M4A1.class.getName());

    public M4A1() {
        // Nome, Tipo, TipoMunição, Dano, Alcance, Precisão, VelocidadeTiro, VelocidadeProjétil, MuniçãoMáxima, MuniçãoAtual, TempoRecarga, ContadorProjéteis, MultiplicadorHeadshot
        super("M4A1", WeaponType.RIFLE, AmmoType._556MM, 0.8, 75, 0.8, 2.0, 130, 30, 30, 2, 3, 1);

        // Configuração de rajada (já tem valores padrão na classe BurstFireWeapon)
        this.setBurstCount(3);        // 3 tiros por rajada
        this.setBurstDelay(3);        // 5 ticks entre tiros da mesma rajada (mais realista)
        this.setBurstMode(true);      // Sempre em modo rajada

        // A M4A1 tem:
        // - Dano médio (2.5 vs 2.0 da AK47)
        // - Alcance menor (75 vs 80 da AK47)
        // - Precisão muito alta (0.8 vs 0.25 da AK47)
        // - Dispara apenas em rajadas de 3 tiros por clique
        // - Velocidade de tiro maior (5.0 vs 4.5 da AK47)
        // - Velocidade de projétil alta (130 vs 90 da AK47)
        // - Munição máxima de 30
        // - Tempo de recarga menor (2 vs 3 da AK47)

        LOGGER.info("M4A1 inicializada com sucesso - Nome: " + getName() +
                   ", Tipo: " + getType() +
                   ", Modo: Rajada (" + getBurstCount() + " tiros)");
    }

    @Override
    public String getName() {
        return "M4A1";
    }

    @Override
    public WeaponType getType() {
        return WeaponType.RIFLE;
    }

    @Override
    public AmmoType getAmmoType() {
        return AmmoType._556MM;
    }
}